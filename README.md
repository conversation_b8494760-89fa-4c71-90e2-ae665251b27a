# MoTask - Modern Todo List Application

A cutting-edge, full-stack todo list application built with React, TypeScript, and Supabase. MoTask focuses on essential task management features with a clean, modern interface and real-time synchronization.

## 🚀 Features

### Core Functionality
- ✅ **Task Management**: Create, edit, delete, and complete tasks
- 📋 **List Organization**: Organize tasks into custom lists
- 🎯 **Priority System**: High, medium, and low priority levels
- 🔍 **Search & Filter**: Find tasks quickly with advanced filtering
- 📱 **Responsive Design**: Optimized for mobile, tablet, and desktop
- ⚡ **Real-time Sync**: Changes appear instantly across all devices
- 🔐 **Secure Authentication**: User registration and login with <PERSON>pa<PERSON> Auth

### User Experience
- 🎨 **Modern UI**: Clean, intuitive interface with Tailwind CSS
- ♿ **Accessibility**: WCAG 2.1 AA compliant
- 🌙 **Theme Support**: Light and dark mode (future enhancement)
- 📱 **Touch Gestures**: Swipe actions on mobile devices
- ⌨️ **Keyboard Shortcuts**: Efficient navigation for power users

## 🏗️ Architecture

### Frontend Stack
- **React 18** with TypeScript for type safety
- **Vite** for fast development and optimized builds
- **Tailwind CSS** for utility-first styling
- **React Router** for client-side navigation
- **Zustand** for lightweight state management

### Backend Stack
- **Supabase** for database, authentication, and real-time features
- **PostgreSQL** with Row Level Security (RLS)
- **JWT Authentication** for secure user sessions
- **Real-time subscriptions** for live updates

## 📁 Project Structure

```
motask/
├── docs/                     # Comprehensive project documentation
│   ├── 01-project-overview.md
│   ├── 02-database-schema.md
│   ├── 03-api-specification.md
│   ├── 04-ui-wireframes.md
│   ├── 05-development-roadmap.md
│   └── 06-technical-requirements.md
├── src/
│   ├── components/           # Reusable UI components
│   │   ├── ui/              # Basic UI elements
│   │   ├── forms/           # Form components
│   │   ├── layout/          # Layout components
│   │   └── features/        # Feature-specific components
│   ├── pages/               # Page components
│   ├── hooks/               # Custom React hooks
│   ├── services/            # API and external services
│   ├── stores/              # State management
│   ├── types/               # TypeScript definitions
│   ├── utils/               # Utility functions
│   └── styles/              # Global styles
├── tests/                   # Test files
└── public/                  # Static assets
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- Git
- Supabase account

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/molder-34/MoTask.git
   cd MoTask
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your Supabase credentials
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

5. **Open in browser**
   ```
   http://localhost:5173
   ```

### Environment Variables

Create a `.env.local` file with the following variables:

```bash
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## 🗄️ Database Setup

### Supabase Configuration

1. Create a new Supabase project
2. Run the SQL scripts from `docs/02-database-schema.md` to set up:
   - Tables (profiles, lists, tasks)
   - Row Level Security policies
   - Triggers and functions
   - Indexes for performance

### Database Schema Overview

```sql
profiles (user data)
├── id (UUID, references auth.users)
├── email (TEXT)
├── full_name (TEXT)
└── avatar_url (TEXT)

lists (task categories)
├── id (UUID)
├── user_id (UUID, references profiles)
├── name (TEXT)
├── description (TEXT)
├── color (TEXT)
└── position (INTEGER)

tasks (todo items)
├── id (UUID)
├── list_id (UUID, references lists)
├── title (TEXT)
├── description (TEXT)
├── completed (BOOLEAN)
├── priority (INTEGER)
└── position (INTEGER)
```

## 🧪 Testing

### Run Tests
```bash
# Unit and component tests
npm run test

# Watch mode for development
npm run test:watch

# Coverage report
npm run test:coverage

# End-to-end tests
npm run test:e2e
```

### Testing Strategy
- **Unit Tests**: Utility functions and business logic
- **Component Tests**: React components with React Testing Library
- **Integration Tests**: API integration and user flows
- **E2E Tests**: Complete user journeys with Playwright

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Deploy to Vercel
```bash
npm run deploy
```

### Environment Setup
1. Set environment variables in your hosting platform
2. Configure custom domain (optional)
3. Set up monitoring and analytics

## 📚 Documentation

Comprehensive documentation is available in the `/docs` folder:

- **[Project Overview](docs/01-project-overview.md)**: Vision, architecture, and design philosophy
- **[Database Schema](docs/02-database-schema.md)**: Complete database design and RLS policies
- **[API Specification](docs/03-api-specification.md)**: Detailed API endpoints and usage
- **[UI Wireframes](docs/04-ui-wireframes.md)**: Design mockups and component specifications
- **[Development Roadmap](docs/05-development-roadmap.md)**: Sprint planning and milestones
- **[Technical Requirements](docs/06-technical-requirements.md)**: Dependencies and system requirements

## 🤝 Contributing

### Development Workflow
1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Run tests (`npm run test`)
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

### Code Standards
- **TypeScript**: Strict mode enabled
- **ESLint**: Airbnb configuration with React hooks
- **Prettier**: Automatic code formatting
- **Husky**: Pre-commit hooks for quality checks

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Supabase** for providing an excellent backend-as-a-service platform
- **React Team** for the amazing React framework
- **Tailwind CSS** for the utility-first CSS framework
- **Vite** for the lightning-fast build tool

## 📞 Support

If you have any questions or need help:

1. Check the [documentation](docs/) first
2. Search existing [GitHub Issues](https://github.com/molder-34/MoTask/issues)
3. Create a new issue if needed
4. Join our community discussions

## 🗺️ Roadmap

### Current Version (v1.0)
- ✅ Core task management
- ✅ Real-time synchronization
- ✅ Mobile-responsive design
- ✅ Search and filtering

### Future Enhancements (v2.0)
- 🔄 Offline support with sync
- 🌙 Dark mode theme
- 📊 Advanced analytics
- 🔗 Task sharing and collaboration
- 📱 Progressive Web App (PWA)
- 🔔 Push notifications

---

**Built with ❤️ by [molder-34](https://github.com/molder-34)**

*MoTask - Making task management simple and efficient.*
